import <PERSON>z<PERSON><PERSON> from 'pizzip';
import Docxtemplater from 'docxtemplater';
import ImageModule from 'docxtemplater-image-module';
import { saveAs } from 'file-saver';
import { DocumentTemplate } from '../types/document_template';
import { getTemplateFileUrl } from './documentTemplateService';

/**
 * Service for handling document templates using Docxtemplater
 */
export class TemplateService {
  /**
   * Loads a template from a URL and returns the template as an ArrayBuffer
   * @param templateId ID of the template to load
   * @returns Promise resolving to the template ArrayBuffer
   */
  static async loadTemplate(templateId: number): Promise<ArrayBuffer> {
    try {
      const response = await fetch(getTemplateFileUrl(templateId));
      if (!response.ok) {
        throw new Error(`Failed to load template: ${response.statusText}`);
      }
      return await response.arrayBuffer();
    } catch (error) {
      console.error('Error loading template:', error);
      throw error;
    }
  }

  /**
   * Fills a template with data and returns the filled document as a blob
   * @param templateContent Template content as ArrayBuffer
   * @param data Data to fill the template with
   * @returns Promise resolving to the filled document blob
   */
  static async fillTemplate(templateContent: ArrayBuffer, data: Record<string, any>): Promise<Blob> {
    try {
      console.log('Original data:', data);

      // Load the template
      const zip = new PizZip(templateContent);

      // Transform template to use image placeholders for signatures
      this.transformTemplateForSignatures(zip);

      // Process the template data and handle signatures
      const processedData = await this.processTemplateData(data);

      console.log('Processed data:', processedData);

      // Base64 regex and validation (from docxtemplater documentation)
      const base64Regex = /^(?:data:)?image\/(png|jpg|jpeg|svg|svg\+xml);base64,/;
      const validBase64 = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/;

      function base64Parser(tagValue: string) {
        if (typeof tagValue !== "string" || !base64Regex.test(tagValue)) {
          return false;
        }

        const stringBase64 = tagValue.replace(base64Regex, "");

        if (!validBase64.test(stringBase64)) {
          throw new Error("Error parsing base64 data, your data contains invalid characters");
        }

        // For browsers, return a Uint8Array buffer (as per docxtemplater docs)
        const binaryString = atob(stringBase64);
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
          const ascii = binaryString.charCodeAt(i);
          bytes[i] = ascii;
        }
        return bytes.buffer;
      }

      // Configure image module for signatures
      const imageModule = new ImageModule({
        centered: false,
        getImage: (tagValue: string, tagName: string) => {
          console.log(`Processing image for tag: ${tagName}`, tagValue ? `has data (${tagValue.length} chars)` : 'no data');

          // Process signature fields (both with and without % prefix)
          const isSignatureField = tagName && (tagName.includes('handtekening') || tagName.endsWith('_handtekening'));

          if (isSignatureField && tagValue) {
            if (typeof tagValue === 'string' && tagValue.startsWith('data:image/')) {
              try {
                const result = base64Parser(tagValue);
                if (result) {
                  console.log(`Successfully processed signature image for ${tagName}: ${(result as ArrayBuffer).byteLength} bytes`);
                  return result;
                } else {
                  console.error('Base64 parser returned false for signature');
                  return null;
                }
              } catch (error) {
                console.error(`Error processing signature image for ${tagName}:`, error);
                return null;
              }
            } else {
              console.warn(`Invalid signature data format for ${tagName}:`, typeof tagValue, tagValue ? tagValue.substring(0, 50) : 'empty');
            }
          }

          // For non-signature fields or empty signatures, return null
          return null;
        },
        getSize: (img: any, tagValue: string, tagName: string) => {
          // Return signature size in EMU (English Metric Units)
          // 1 inch = 914400 EMU, so this is roughly 2.5 inches wide by 1.2 inches tall
          const isSignatureField = tagName && (tagName.includes('handtekening') || tagName.endsWith('_handtekening'));

          if (isSignatureField) {
            return [2286000, 1097280]; // 2.5" x 1.2"
          }
          return [1828800, 914400]; // Default size
        }
      });

      // Create a new instance of Docxtemplater with image module
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
        modules: [imageModule],
        delimiters: {
          start: '{',
          end: '}'
        },
        nullGetter: (part: any) => {
          // Handle null values for signature fields
          if (part.value && (part.value.includes('handtekening') || part.value.startsWith('%'))) {
            return '';
          }
          return '';
        }
      });

      // Render the document (replace all variables with their values)
      try {
        console.log('About to render document with data:', Object.keys(processedData));
        doc.render(processedData);
        console.log('Document rendered successfully');
      } catch (renderError: any) {
        console.error('Error during document rendering:', renderError);

        // Check if it's an image module error
        if (renderError.message && renderError.message.includes('image')) {
          console.error('Image module error details:', renderError);
        }

        throw new Error(`Document rendering failed: ${renderError.message || renderError}`);
      }

      // Get the zip document containing the filled template
      const out = doc.getZip().generate({
        type: 'blob',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });

      return out;
    } catch (error) {
      console.error('Error filling template:', error);
      throw error;
    }
  }



  /**
   * Process template data to handle signatures and other special fields
   * @param data Raw template data
   * @returns Processed data ready for template filling
   */
  private static async processTemplateData(data: Record<string, any>): Promise<Record<string, any>> {
    const processedData = { ...data };

    // Process signature fields
    for (const [key, value] of Object.entries(processedData)) {
      if (key.includes('handtekening')) {
        if (typeof value === 'string' && value.startsWith('data:image/')) {
          // Validate the signature data
          try {
            const base64Data = value.split(',')[1];
            if (base64Data && base64Data.length > 0) {
              // Test if base64 is valid
              atob(base64Data);
              console.log(`Valid signature found for field: ${key} (${base64Data.length} chars)`);

              // For the transformed template, we need to provide data for the % prefixed keys
              // The template transformation converts {klant_handtekening} to {%klant_handtekening}
              if (key === 'klant_handtekening') {
                processedData['%klant_handtekening'] = value;
                processedData[key] = ''; // Clear original to avoid showing base64 string
              } else if (key === 'monteur_handtekening') {
                processedData['%monteur_handtekening'] = value;
                processedData[key] = ''; // Clear original to avoid showing base64 string
              } else {
                // For other signature fields, keep both formats
                processedData[key] = value;
                processedData[`%${key}`] = value;
              }

            } else {
              console.warn(`Invalid base64 data for signature field: ${key}`);
              this.setEmptySignature(processedData, key);
            }
          } catch (error) {
            console.error(`Error validating signature for field ${key}:`, error);
            this.setEmptySignature(processedData, key);
          }
        } else if (value === '' || value === null || value === undefined) {
          // Empty signature - this is valid, just set to empty string
          console.log(`Empty signature for field: ${key}`);
          this.setEmptySignature(processedData, key);
        } else {
          // Invalid signature format
          console.warn(`Invalid signature format for field: ${key}`, typeof value, value);
          this.setEmptySignature(processedData, key);
        }
      }
    }

    console.log('Processed template data:', Object.keys(processedData).filter(k => k.includes('handtekening')).map(k => `${k}: ${processedData[k] ? 'has signature' : 'empty'}`));

    return processedData;
  }

  /**
   * Helper function to set empty signature values for both regular and image placeholders
   * @param processedData The data object to update
   * @param key The signature field key
   */
  private static setEmptySignature(processedData: Record<string, any>, key: string): void {
    if (key === 'klant_handtekening') {
      processedData['%klant_handtekening'] = '';
      processedData[key] = '';
    } else if (key === 'monteur_handtekening') {
      processedData['%monteur_handtekening'] = '';
      processedData[key] = '';
    } else {
      processedData[key] = '';
      processedData[`%${key}`] = '';
    }
  }

  /**
   * Transform template to use image placeholders for signature fields
   * @param zip PizZip instance containing the template
   */
  private static transformTemplateForSignatures(zip: PizZip): void {
    try {
      // Get the main document content
      const documentXml = zip.file('word/document.xml');
      if (documentXml) {
        let content = documentXml.asText();

        // Transform signature placeholders to image placeholders
        // Replace {klant_handtekening} with {%klant_handtekening}
        // Replace {monteur_handtekening} with {%monteur_handtekening}
        content = content.replace(/\{klant_handtekening\}/g, '{%klant_handtekening}');
        content = content.replace(/\{monteur_handtekening\}/g, '{%monteur_handtekening}');

        // Update the file in the zip
        zip.file('word/document.xml', content);

        console.log('Template transformed: signature placeholders converted to image placeholders');
      }
    } catch (error) {
      console.error('Error transforming template for signatures:', error);
      // Continue without transformation if there's an error
    }
  }

  /**
   * Downloads a filled template
   * @param blob Blob containing the filled template
   * @param fileName Name of the file to download
   */
  static downloadDocument(blob: Blob, fileName: string): void {
    saveAs(blob, fileName);
  }

  /**
   * Saves a filled template to the server
   * @param templateId ID of the template used
   * @param customerId ID of the customer
   * @param blob Blob containing the filled template
   * @param fileName Name of the file to save
   * @returns Promise resolving to the saved document
   */
  static async saveDocument(templateId: number, customerId: number, blob: Blob, fileName: string): Promise<any> {
    const { saveFilledTemplate } = await import('./documentTemplateService');
    return saveFilledTemplate(templateId, customerId, blob, fileName);
  }

  /**
   * Analyzes a template to extract form fields
   * @param templateContent Template content as ArrayBuffer
   * @returns Promise resolving to an object with field information
   */
  static async analyzeTemplate(templateContent: ArrayBuffer): Promise<{
    fields: Array<{name: string, label: string}>;
    checkboxes: Array<{name: string, label: string}>;
    structure?: any;
    isInspectionTemplate?: boolean;
  }> {
    try {
      // Load the template
      const zip = new PizZip(templateContent);

      // Get the main document content
      const content = zip.files['word/document.xml']?.asText() || '';

      // Check if this is an inspection template based on content
      const isInspectionTemplate = content.includes('Type installatie') ||
                                   content.includes('Centrale / kiezer') ||
                                   content.includes('inbraakmeldsysteem') ||
                                   content.includes('centrale_accu');

      if (isInspectionTemplate) {
        return this.analyzeInspectionTemplate(content);
      }

      // Extract fields using regex for regular templates
      const fieldsMap = new Map<string, string>();
      const checkboxesMap = new Map<string, string>();

      // Look for simple fields in the format {field_name}
      const simpleFieldRegex = /{([^{}#/]+)}/g;
      let match;

      while ((match = simpleFieldRegex.exec(content)) !== null) {
        const fieldName = match[1].trim();
        // Skip if it's part of a conditional
        if (!content.includes(`{#if ${fieldName}}`) && !fieldName.includes('if') && !fieldName.includes('else')) {
          fieldsMap.set(fieldName, this.formatFieldLabel(fieldName));
        }
      }

      // Look for conditional fields (checkboxes) in the format {#field_name} or {#if field_name}
      const conditionalFieldRegex = /{#(?:if\s+)?([^{}]+)}/g;

      while ((match = conditionalFieldRegex.exec(content)) !== null) {
        const fieldName = match[1].trim();
        // Skip if it contains "else" or other template keywords
        if (!fieldName.includes('else') && !fieldName.includes('/') && !fieldName.includes('^')) {
          checkboxesMap.set(fieldName, this.formatFieldLabel(fieldName));
        }
      }

      // Convert maps to arrays
      const fields = Array.from(fieldsMap).map(([name, label]) => ({ name, label }));
      const checkboxes = Array.from(checkboxesMap).map(([name, label]) => ({ name, label }));

      return { fields, checkboxes };
    } catch (error) {
      console.error('Error analyzing template:', error);
      throw error;
    }
  }

  /**
   * Analyzes inspection template structure
   */
  static analyzeInspectionTemplate(content: string) {
    const structure = {
      customerFields: [
        { name: 'bonnummer', label: 'Bonnummer', type: 'text', autoPopulate: true },
        { name: 'klantnummer', label: 'Klantnummer', type: 'text', autoPopulate: true },
        { name: 'bedrijf', label: 'Bedrijf', type: 'text', autoPopulate: true },
        { name: 'adres', label: 'Adres', type: 'text', autoPopulate: true },
        { name: 'telefoon', label: 'Telefoonnummer', type: 'text', autoPopulate: true },
        { name: 'contactpersoon', label: 'Contactpersoon', type: 'text', autoPopulate: true },
        { name: 'email', label: 'Email', type: 'text', autoPopulate: true },
        { name: 'type', label: 'Type', type: 'text', autoPopulate: false }
      ],
      installationTypes: [
        { name: 'inbraakmeldsysteem', label: 'Inbraakmeldsysteem' },
        { name: 'brandmeldsysteem', label: 'Brandmeldsysteem' },
        { name: 'cctv', label: 'CCTV' }
      ],
      sections: [
        {
          title: 'Centrale / kiezer',
          components: [
            { name: 'centrale_accu', label: 'Accu' },
            { name: 'centrale_voeding', label: 'Voeding' },
            { name: 'centrale_lusspanning', label: 'Lusspanning' },
            { name: 'centrale_uitlezing', label: 'Uitlezing' },
            { name: 'centrale_algemeen', label: 'Algemene werking' }
          ]
        },
        {
          title: 'Detectie',
          components: [
            { name: 'detectie_bevestiging', label: 'Bevestiging' },
            { name: 'detectie_werking', label: 'Werking' },
            { name: 'detectie_projectie', label: 'Projectie' },
            { name: 'detectie_algemeen', label: 'Detectie algemeen' }
          ]
        },
        {
          title: 'Bekabeling',
          components: [
            { name: 'bekabeling_bevestiging', label: 'Bevestiging' },
            { name: 'bekabeling_afscherming', label: 'Afscherming' }
          ]
        },
        {
          title: 'Signalering',
          components: [
            { name: 'signalering_bevestiging', label: 'Bevestiging' },
            { name: 'signalering_werking_flits', label: 'Werking flits' },
            { name: 'signalering_werking_sirene', label: 'Werking sirene' },
            { name: 'signalering_algemeen', label: 'Signalering algemeen' }
          ]
        },
        {
          title: 'Doormelding',
          components: [
            { name: 'doormelding_schakel', label: 'Schakelmelding' },
            { name: 'doormelding_inbraak', label: 'Inbraak' },
            { name: 'doormelding_overval', label: 'Overval' },
            { name: 'doormelding_brand', label: 'Brand' },
            { name: 'doormelding_technisch', label: 'Technisch' },
            { name: 'doormelding_contact', label: 'Contact gewenst MK' }
          ]
        }
      ],
      generalState: [
        { name: 'installatie_in_orde', label: 'Installatie in orde' },
        { name: 'installatie_niet_in_orde', label: 'Installatie niet in orde' },
        { name: 'ja_opmerking', label: 'Ja, opmerking', type: 'textarea' },
        { name: 'nee_onbekend', label: 'Nee / onbekend', type: 'textarea' }
      ],
      signatures: [
        { name: 'klant_naam', label: 'Klant naam', type: 'text' },
        { name: 'datum', label: 'Datum', type: 'date' },
        { name: 'klant_handtekening', label: 'Klant handtekening', type: 'signature' },
        { name: 'monteur_naam', label: 'Monteur naam', type: 'text' },
        { name: 'begin_tijd', label: 'Begin tijd', type: 'time' },
        { name: 'eind_tijd', label: 'Eind tijd', type: 'time' },
        { name: 'monteur_handtekening', label: 'Monteur handtekening', type: 'signature' }
      ]
    };

    return {
      fields: [],
      checkboxes: [],
      structure,
      isInspectionTemplate: true
    };
  }

  /**
   * Formats a field name into a human-readable label
   * @param fieldName The raw field name from the template
   * @returns A formatted label
   */
  static formatFieldLabel(fieldName: string): string {
    // Remove prefixes like "check_"
    let label = fieldName.replace(/^check_/, '');

    // Replace underscores with spaces
    label = label.replace(/_/g, ' ');

    // Capitalize first letter of each word
    label = label.replace(/\b\w/g, c => c.toUpperCase());

    return label;
  }
}

export default TemplateService;
